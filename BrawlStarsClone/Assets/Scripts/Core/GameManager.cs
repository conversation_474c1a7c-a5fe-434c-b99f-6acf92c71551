using UnityEngine;
using System.Collections.Generic;

public class GameManager : MonoBehaviour
{
    public static GameManager Instance;
    public float matchDuration = 150f;
    public GameState currentState;
    public float timeRemaining;
    
    void Awake()
    {
        Instance = this;
    }
    
    void Start()
    {
        currentState = GameState.Playing;
        timeRemaining = matchDuration;
    }
}

public enum GameState { Waiting, Playing, Finished }
