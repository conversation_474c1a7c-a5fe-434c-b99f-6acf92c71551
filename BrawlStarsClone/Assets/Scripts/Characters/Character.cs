using UnityEngine;

public abstract class Character : MonoBehaviour
{
    public int maxHealth = 3000;
    public int currentHealth;
    public float moveSpeed = 5f;
    
    protected virtual void Start()
    {
        currentHealth = maxHealth;
    }
    
    public virtual void TakeDamage(int damage)
    {
        currentHealth -= damage;
        if (currentHealth <= 0) Die();
    }
    
    protected virtual void Die()
    {
        gameObject.SetActive(false);
    }
}
