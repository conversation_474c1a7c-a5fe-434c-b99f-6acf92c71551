# 폴더 구조 생성
mkdir -p BrawlStarsClone/Assets/Scripts/{Core,Characters,Skills,Projectiles,Map,UI,GameModes,Items}
mkdir -p BrawlStarsClone/Assets/{Prefabs,Resources/Prefabs/Characters,Scenes}

cd BrawlStarsClone

# GameManager.cs 파일 생성
echo 'using UnityEngine;
using System.Collections.Generic;

public class GameManager : MonoBehaviour
{
    public static GameManager Instance;
    public float matchDuration = 150f;
    public GameState currentState;
    public float timeRemaining;
    
    void Awake()
    {
        Instance = this;
    }
    
    void Start()
    {
        currentState = GameState.Playing;
        timeRemaining = matchDuration;
    }
}

public enum GameState { Waiting, Playing, Finished }' > Assets/Scripts/Core/GameManager.cs

# Character.cs 파일 생성
echo 'using UnityEngine;

public abstract class Character : MonoBehaviour
{
    public int maxHealth = 3000;
    public int currentHealth;
    public float moveSpeed = 5f;
    
    protected virtual void Start()
    {
        currentHealth = maxHealth;
    }
    
    public virtual void TakeDamage(int damage)
    {
        currentHealth -= damage;
        if (currentHealth <= 0) Die();
    }
    
    protected virtual void Die()
    {
        gameObject.SetActive(false);
    }
}' > Assets/Scripts/Characters/Character.cs

echo "프로젝트 파일들이 생성되었습니다!"
echo "BrawlStarsClone 폴더를 Unity에서 열어주세요!"